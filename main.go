// docgen.go
// Single-file document generation engine for Vedic‑astrology (or any) reports.
// -----------------------------------------------------------------------------
// How it works
// ------------
//  1. Author a *workflow* markdown file where every step is a fenced block that
//     begins with ```step followed by key=value attributes. Supported types:
//     - input   : interactive questions → context variables
//     - code    : arbitrary Go program executed with `go run`; prints JSON → context variable
//     - prompt  : LLM call (OpenAI) with Go‑template substitution
//     - template: Go template rendered to stdout / optional file
//  2. Run:  go run docgen.go workflow.md
//  3. The engine executes steps sequentially, maintaining a shared map[string]any
//     called *ctx*. Each step can read {{ .Variable }} values from previous steps.
//
// External dependencies:
//
//	go get github.com/sashabaranov/go-openai   // LLM
//	go get github.com/traefik/yaegi/interp      // optional: inline Go exec (not used when go run)
//	go get github.com/mshafiee/swephgo         // Swiss‑Ephemeris bindings (use inside code steps)
//
// Set OPENAI_API_KEY in your environment before running prompt steps.
// -----------------------------------------------------------------------------
package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"text/template"
	"time"

	openai "github.com/sashabaranov/go-openai"
)

// -----------------------------
// STEP PARSING
// -----------------------------

// StepType enum
const (
	StepInput    = "input"
	StepCode     = "code"
	StepPrompt   = "prompt"
	StepTemplate = "template"
)

type Step struct {
	ID      string
	Type    string
	Attr    map[string]string // key=value attributes on the opening ```step line
	Content string            // body until closing fence
}

// ParseSteps walks the markdown and extracts fenced `step` blocks in order.
func ParseSteps(md string) ([]Step, error) {
	var steps []Step

	scanner := bufio.NewScanner(strings.NewReader(md))
	fenceRe := regexp.MustCompile("^```step(.*)$")
	inBlock := false
	var cur Step
	var body strings.Builder

	for scanner.Scan() {
		line := scanner.Text()
		if !inBlock {
			if m := fenceRe.FindStringSubmatch(line); m != nil {
				// We are entering a step fence.
				attrStr := strings.TrimSpace(m[1])
				cur = Step{Attr: map[string]string{}}
				for _, token := range strings.Split(attrStr, " ") {
					if token == "" {
						continue
					}
					kv := strings.SplitN(token, "=", 2)
					if len(kv) == 2 {
						cur.Attr[kv[0]] = kv[1]
					}
				}
				cur.Type = cur.Attr["type"]
				cur.ID = cur.Attr["id"]
				inBlock = true
				body.Reset()
			}
			continue
		}
		// inside block
		if strings.HasPrefix(line, "```") {
			cur.Content = body.String()
			steps = append(steps, cur)
			inBlock = false
			continue
		}
		body.WriteString(line)
		body.WriteString("\n")
	}
	if inBlock {
		return nil, errors.New("unterminated ```step block")
	}
	return steps, nil
}

// -----------------------------
// MAIN EXECUTION ENGINE
// -----------------------------

type ContextMap map[string]any

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: docgen <workflow.md>")
		os.Exit(1)
	}
	workflowPath := os.Args[1]
	md, err := ioutil.ReadFile(workflowPath)
	if err != nil {
		log.Fatalf("could not read %s: %v", workflowPath, err)
	}

	steps, err := ParseSteps(string(md))
	if err != nil {
		log.Fatalf("parse error: %v", err)
	}

	ctx := ContextMap{}
	for _, st := range steps {
		fmt.Printf("\n--- Executing step %s (%s) ---\n", st.ID, st.Type)
		switch st.Type {
		case StepInput:
			if err := runInputStep(st, ctx); err != nil {
				fatal(st.ID, err)
			}
		case StepCode:
			if err := runCodeStep(st, ctx); err != nil {
				fatal(st.ID, err)
			}
		case StepPrompt:
			if err := runPromptStep(st, ctx); err != nil {
				fatal(st.ID, err)
			}
		case StepTemplate:
			if err := runTemplateStep(st, ctx); err != nil {
				fatal(st.ID, err)
			}
		default:
			fatal(st.ID, fmt.Errorf("unknown step type %q", st.Type))
		}
	}
}

func fatal(stepID string, err error) {
	log.Fatalf("\n[error] step %s failed: %v\n", stepID, err)
}

// -----------------------------
// STEP EXECUTORS
// -----------------------------

type inputField struct {
	Name    string `json:"name"`
	Prompt  string `json:"prompt"`
	Default string `json:"default,omitempty"`
}

func runInputStep(st Step, ctx ContextMap) error {
	var fields []inputField
	if err := json.Unmarshal([]byte(st.Content), &fields); err != nil {
		return fmt.Errorf("input step %s: %v", st.ID, err)
	}
	rd := bufio.NewReader(os.Stdin)
	for _, f := range fields {
		prompt := f.Prompt
		if prompt == "" {
			prompt = f.Name + ":"
		}
		if f.Default != "" {
			prompt += fmt.Sprintf(" [%s]", f.Default)
		}
		prompt += " "
		fmt.Print(prompt)
		val, _ := rd.ReadString('\n')
		val = strings.TrimSpace(val)
		if val == "" {
			val = f.Default
		}
		ctx[f.Name] = val
	}
	return nil
}

func runCodeStep(st Step, ctx ContextMap) error {
	outputVar := st.Attr["output"]
	if outputVar == "" {
		return errors.New("code step missing output=<var> attribute")
	}

	tmpDir, err := ioutil.TempDir("", "docgen-code-")
	if err != nil {
		return err
	}
	defer os.RemoveAll(tmpDir)

	mainPath := filepath.Join(tmpDir, "main.go")
	if err := ioutil.WriteFile(mainPath, []byte(st.Content), fs.FileMode(0644)); err != nil {
		return err
	}

	ctxJSON, _ := json.Marshal(ctx)
	cmd := exec.Command("go", "run", mainPath)
	cmd.Env = append(os.Environ(), "DOCGEN_CONTEXT="+string(ctxJSON))
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("code step %s: %v", st.ID, err)
	}

	// capture JSON from stdout
	var result any
	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return fmt.Errorf("code step %s (unmarshal): %v", st.ID, err)
	}
	ctx[outputVar] = result
	return nil
}

func runPromptStep(st Step, ctx ContextMap) error {
	outputVar := st.Attr["output"]
	if outputVar == "" {
		return errors.New("prompt step missing output=<var>")
	}
	model := st.Attr["model"]
	if model == "" {
		model = "gpt-4o"
	}
	systemMsg := st.Attr["system"]

	// Substitute context vars using Go templates
	tmpl, err := template.New("prompt").Parse(st.Content)
	if err != nil {
		return err
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, ctx); err != nil {
		return err
	}

	prompt := buf.String()

	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		return errors.New("OPENAI_API_KEY not set")
	}
	cli := openai.NewClient(apiKey)
	resp, err := cli.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model: model,
		Messages: []openai.ChatCompletionMessage{
			{Role: openai.ChatMessageRoleSystem, Content: systemMsg},
			{Role: openai.ChatMessageRoleUser, Content: prompt},
		},
	})
	if err != nil {
		return err
	}
	if len(resp.Choices) == 0 {
		return errors.New("empty response from LLM")
	}
	answer := strings.TrimSpace(resp.Choices[0].Message.Content)
	ctx[outputVar] = answer
	return nil
}

func runTemplateStep(st Step, ctx ContextMap) error {
	dest := st.Attr["file"] // optional output file path
	tmpl, err := template.New(st.ID).Parse(st.Content)
	if err != nil {
		return err
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, ctx); err != nil {
		return err
	}

	if dest != "" {
		if err := ioutil.WriteFile(dest, buf.Bytes(), 0644); err != nil {
			return err
		}
		fmt.Printf("rendered → %s\n", dest)
	} else {
		fmt.Println(buf.String())
	}
	return nil
}

// -----------------------------
// HELPERS (optional)
// -----------------------------

// ParseDateTime is a convenience helper used inside code steps via the context.
// It combines a date (YYYY‑MM‑DD) and time (HH:MM, 24h) into a Go time.Time
// object in UTC.
func ParseDateTime(dateStr, timeStr string) (time.Time, error) {
	layout := "2006-01-02 15:04"
	return time.Parse(layout, dateStr+" "+timeStr)
}
