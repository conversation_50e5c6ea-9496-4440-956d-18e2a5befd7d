 
# Minimal Vedic Astrology Workflow
Your answers are stored in a private context and never sent anywhere except the LLM call.

```step id=collect type=input
[
  {"name":"birth_date","prompt":"Birth date (YYYY-MM-DD)"},
  {"name":"birth_time","prompt":"Birth time (HH:MM, 24h)"},
  {"name":"timezone","prompt":"Timezone offset from UTC (e.g. +05:30)"},
  {"name":"lat","prompt":"Latitude (+N, -S)",   "default":"11.2588"},
  {"name":"lon","prompt":"Longitude (+E, -W)",  "default":"75.7804"}
]
````

```step id=chart type=code output=chart
package main

import (
	"encoding/json"
	"os"
	"strconv"
	"strings"
	"time"

	swe "github.com/mshafiee/swephgo"
)

type Planet struct {
	Name      string  `json:"name"`
	Longitude float64 `json:"longitude_deg"`
}

func must(err error) {
	if err != nil {
		panic(err)
	}
}

func main() {
	var ctx map[string]any
	must(json.Unmarshal([]byte(os.Getenv("DOCGEN_CONTEXT")), &ctx))

	// ----- convert inputs ----------------------------------------------------
	date := ctx["birth_date"].(string)
	tm   := ctx["birth_time"].(string)
	off  := ctx["timezone"].(string)

	locH, locM := 0, 0
	if strings.Contains(off, ":") {
		p := strings.Split(off, ":")
		locH, _ = strconv.Atoi(p[0])
		locM, _ = strconv.Atoi(p[1])
	} else {
		locH, _ = strconv.Atoi(off)
	}
	seconds := locH*3600 + locM*60
	loc := time.FixedZone("native", seconds)

	t, _ := time.ParseInLocation("2006-01-02 15:04", date+" "+tm, loc)
	jd := swe.SweJulday(t.Year(), int(t.Month()), t.Day(),
		float64(t.Hour())+float64(t.Minute())/60.0,
		swe.SweGregCalendar)

	// ----- calculate geocentric ecliptic longitudes --------------------------
	flags   := swe.SEFLG_SWIEPH | swe.SEFLG_SPEED
	planets := []int{swe.SE_SUN, swe.SE_MOON, swe.SE_MERCURY, swe.SE_VENUS,
	                 swe.SE_MARS, swe.SE_JUPITER, swe.SE_SATURN,
	                 swe.SE_TRUE_NODE}

	out := make([]Planet, 0, len(planets))
	for _, p := range planets {
		x := make([]float64, 6)
		swe.SweCalc(jd, p, flags, x)
		out = append(out, Planet{
			Name:      swe.SweGetPlanetName(p),
			Longitude: x[0],
		})
	}
	json.NewEncoder(os.Stdout).Encode(out)
}
```

```step id=reading type=prompt output=analysis model=gpt-4o system="You are an expert Vedic astrologer."
Using the planetary longitudes below (in degrees, Lahiri ayanāṃśa assumed), write an in-depth Vedic interpretation focusing on the native’s strengths, challenges, and remedies.

{{ .chart }}
```

```step id=report type=template file=vedic_report.md
# Personalized Vedic Astrology Report

**Date/time (UTC)** : {{ .birth_date }} {{ .birth_time }}  
**Latitude / Longitude** : {{ .lat }} / {{ .lon }}

---

## Planetary Positions

| Planet | Longitude (°) |
|--------|---------------|
{{- range .chart }}
| {{ .Name }} | {{ printf \"%.2f\" .Longitude }} |
{{- end }}

---

## Expert Analysis

{{ .analysis }}

---
*Generated on {{ now }} with Swiss-Ephemeris & GPT-4o.*
```

 